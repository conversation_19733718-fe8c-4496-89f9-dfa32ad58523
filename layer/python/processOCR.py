from tqdm import tqdm # type: ignore
import json
import sys

from preProcessPDF import extract_page, get_pdf_reader_and_metadata
from model import batchEvaluateRequest, batchGenerateRequest, generate, evaluate
from prompts import create_rubric_prompt, all_prompts
# from model import batchProcessing
from concurrent.futures import ThreadPoolExecutor
import re
from langchain_core.documents import Document
from mongoOps import get_mongo_client, save_progress

subject_to_prompt_mapping = {
    "Paper A: Compulsory Indian Language": "paper_a_indian_language",
    "Paper B: Compulsory English": "paper_b_english",
    "General Studies Paper I": "gs_1",
    "General Studies Paper II": "gs_2",
    "General Studies Paper III": "gs_3",
    "General Studies Paper IV": "gs_4",
    "Essay": "essay",
    "Optional Subject": "optional_subject"
}

def reorder_pages(pages, generic_metadata):
    '''
    Return type should be list of {page_content: "content", metadata: {generic + page number}}
    '''
    pattern = r'page_number:\s*\"?(\d+)\"?'

    documentList = []

    for page in pages:
        # find the page number in the content
        match = re.search(pattern, page)
        metadata = generic_metadata.copy()
        if match:
            page_number = int(match.group(1))
            metadata['page'] = page_number
            documentList.append(Document(page_content= page, metadata=metadata))
        else:
            print(f'[Mehul] page number not found in the content')

    # sort the documents by metadata page number
    documentList.sort(key=lambda doc: doc.metadata.get('page', 0))
    return documentList

def process_all_pages(pdf_path, file_type, subject, jobid, mongoClient):
    """
    Processes all pages in a PDF and returns an array of Document objects.
    """
    reader, metadata = get_pdf_reader_and_metadata(pdf_path)
    documents = []
    # inline_batch_ocr_request = []
    
    for page_number in tqdm(range(len(reader.pages)), desc=f"Processing {file_type} pages"):
        page_metadata = metadata.copy()
        page_metadata['page'] = page_number + 1
        page_metadata['jobid'] = jobid
        save_progress(mongoClient, page_number/len(reader.pages), jobid)
        
        b64_page = extract_page(reader, page_number)
        print(f"[Mehul] Subject in process_all_pages: {subject}, jobid: {jobid}")
        if subject in subject_to_prompt_mapping:
            print(f"[Mehul] Subject found in subject_to_prompt_mapping")
            prompt = all_prompts[subject_to_prompt_mapping[subject]]["answer_sheet_ocr"] if file_type == "answer_sheet" else all_prompts[subject_to_prompt_mapping[subject]]["rubric_ocr"] if file_type == "rubric" else create_rubric_prompt
        else:
            print(f"[Mehul] Subject NOT found in subject_to_prompt_mapping")
            prompt = create_rubric_prompt # Default prompt to prevent crash
        document_page = generate(b64_page, prompt, page_metadata)
        # inline_batch_ocr_request.append(batchGenerateRequest(b64_page, answer_sheet_prompt if file_type == "answer_sheet" else rubric_prompt if file_type == "rubric" else create_rubric_prompt, str(page_number)))
        documents.append(document_page)

    # update to 100 % completion last update before closing client
    save_progress(mongoClient, 1.0, jobid)

    # ocr_result = []
    # if (len(inline_batch_ocr_request) > 0):
    #     ocr_result, _ = batchProcessing(inline_batch_ocr_request, prompt=answer_sheet_prompt if file_type == "answer_sheet" else rubric_prompt if file_type == "rubric" else create_rubric_prompt, write_result=False)
        
    # return reorder_pages(ocr_result, metadata)# [{page_content: "content", metadata: {}}]
    return documents

def removeDuplicates(data):
    unique = []
    seen = set()
    for item in data:
        if item['questionNumber'] not in seen:
            seen.add(item['questionNumber'])
            unique.append(item)
    return unique


def combine_and_evaluate(answer_sheet_docs, rubric_data, subject, jobId, client):
    """
    Optimized version that accepts document objects directly instead of file paths.
    answer_sheet_docs: List of Document objects from OCR
    rubric_data: String content of rubric (from file path or direct content)
    """

    # Extract content from answer sheet documents
    answer_sheet_data = ""
    if isinstance(answer_sheet_docs, list):
        # If it's a list of Document objects
        answer_sheet_data = extract_content_from_docs(answer_sheet_docs)
    else:
        # Fallback: if it's a file path (for backward compatibility)
        with open(answer_sheet_docs, 'r') as jsonl_file:
            for line in jsonl_file:
                try:
                    data = json.loads(line)
                    answer_sheet_data += data['page_content'] + "\n\n"
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSONL line in answer sheet: {str(e)}", file=sys.stderr)
                except Exception as e:
                    print(f"Some other error please check: {str(e)}", file=sys.stderr)

    print("Answer sheet data extracted successfully", file=sys.stderr)
    print("Rubric data extracted successfully", file=sys.stderr)

    evaluation_prompt = all_prompts[subject_to_prompt_mapping[subject]]["evaluation"]
    filter_prompt_answer = all_prompts["ocrChecker"]

    try:
        save_progress(client, 1.0, jobId, "evaluating")
        filterAnswerSheet = json.loads(evaluate(answer_sheet_data, rubric_data, filter_prompt_answer, format='application/json').page_content)
        # print(f"[Mehul] filterAnswerSheet: {filterAnswerSheet}", file=sys.stderr)
        rubric_content = json.loads(rubric_data)
        filterAnswerSheet['answerData'] = removeDuplicates(filterAnswerSheet['answerData'])
        rubric_content['questionsData'] = removeDuplicates(rubric_content['questionsData'])
        # print(f"[Mehul] filterAnswerSheet: {filterAnswerSheet}", file=sys.stderr)
        print(f"[Mehul] got answer Sheet data len: {len(filterAnswerSheet['answerData'])}, rubric data len: {len(rubric_content['questionsData'])}", file=sys.stderr)
        # threadpool to break it in the questions and evalutate each question simultaneously
        numThreads = 10
        questionEvaluation = []
        # for i in range(len(filterAnswerSheet['answerData'])):
        #     questionEvaluation.append(evaluate(filterAnswerSheet['answerData'][i]['answerText'], rubric_content['questionsData'][i]['questionText'], evaluation_prompt))
        if (len(filterAnswerSheet['answerData']) == len(rubric_content['questionsData'])):
            with ThreadPoolExecutor(max_workers=numThreads) as executor:
                questionEvaluation = list(executor.map(lambda comb: evaluate(comb[0]['answerText'], comb[1]['questionText'], evaluation_prompt), zip(filterAnswerSheet['answerData'], rubric_content['questionsData'])))
        # evaluation_result = evaluate(filterAnswerSheet.page_content, rubric_data, evaluation_prompt)
        print(f"[Mehul] questionEvaluation: {questionEvaluation}", file=sys.stderr)
        save_progress(client, 1.0, jobId, "completed")
        # return str(filterAnswerSheet), evaluation_result
        return str(filterAnswerSheet), Document(page_content=str(questionEvaluation))
    except Exception as e:
        raise RuntimeError(f"Error during evaluation: {str(e)}")
    
    
def extract_content_from_docs(docs):
    """
    Helper function to extract page_content from a list of Document objects.
    """
    content = ""
    for doc in docs:
        if hasattr(doc, 'page_content'):
            content += doc.page_content + "\n\n"
        else:
            print(f"Warning: Document object missing page_content: {doc}", file=sys.stderr)
    return content

def create_rubric(question_paper_docs, save_md_files=False):
    """
    Creates a rubric for a question paper.
    Optimized to accept Document objects directly instead of file paths.
    """

    # Extract content from documents
    if isinstance(question_paper_docs, list):
        # Direct document objects
        rubric_data = extract_content_from_docs(question_paper_docs)
    else:
        # Fallback: file path (for backward compatibility)
        rubric_data = ""
        with open(question_paper_docs, 'r') as jsonl_file:
            for line in jsonl_file:
                try:
                    data = json.loads(line)
                    rubric_data += data['page_content'] + "\n\n"
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSONL line in created rubric: {str(e)}", file=sys.stderr)

    # Optional: Save to markdown file for debugging
    # if save_md_files:
    #     with open('/tmp/created_rubric_data.md', 'w') as md_file:
    #         md_file.write(rubric_data)
    #     print("Debug: Created rubric data saved to markdown file", file=sys.stderr)

    print("Created rubric data extracted successfully", file=sys.stderr)

    return rubric_data

